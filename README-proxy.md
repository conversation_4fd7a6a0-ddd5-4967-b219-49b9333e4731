# Cassidy AI to OpenAI API Proxy

将Cassidy AI的聊天接口转换为OpenAI v1兼容的API格式，支持流式/非流式响应和多模态输入。

## 🚀 功能特性

- ✅ **OpenAI v1 API兼容** - 完全兼容OpenAI聊天完成接口
- 🌊 **流式响应支持** - 实时流式输出，支持SSE格式
- 📝 **非流式响应** - 传统的一次性完整响应
- 🖼️ **多模态支持** - 支持文本和图片输入（图片需要预先上传）
- 🧠 **思考内容** - AI的推理过程用`<think></think>`标签包裹
- 🔄 **自动重连** - 内置连接重试和错误处理
- 📊 **健康检查** - 提供服务状态监控端点

## 📋 API端点

### 聊天完成
```
POST /v1/chat/completions
```

### 模型列表
```
GET /v1/models
```

### 健康检查
```
GET /health
```

## 🛠️ 安装和运行

### 前置要求
- [Deno](https://deno.land/) 1.40+

### 启动服务器
```bash
# 默认端口8000
deno run --allow-net --allow-env cassidy-openai-proxy.ts

# 自定义端口
PORT=3000 deno run --allow-net --allow-env cassidy-openai-proxy.ts
```

### 运行测试
```bash
# 运行所有测试
deno run --allow-net test-proxy.ts

# 只测试健康检查
deno run --allow-net test-proxy.ts --health

# 只测试聊天功能
deno run --allow-net test-proxy.ts --chat
```

## 📖 使用示例

### 非流式聊天
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "cassidy-claude-3-7-sonnet-thinking",
    "messages": [
      {"role": "user", "content": "你好，请介绍一下你自己"}
    ],
    "stream": false
  }'
```

### 流式聊天
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "cassidy-claude-3-7-sonnet-thinking",
    "messages": [
      {"role": "user", "content": "请解释什么是人工智能"}
    ],
    "stream": true
  }'
```

### 多轮对话
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "cassidy-claude-3-7-sonnet-thinking",
    "messages": [
      {"role": "system", "content": "你是一个有用的AI助手"},
      {"role": "user", "content": "什么是机器学习？"},
      {"role": "assistant", "content": "机器学习是人工智能的一个分支..."},
      {"role": "user", "content": "能举个具体例子吗？"}
    ],
    "stream": false
  }'
```

### 多模态输入（图片+文本）
```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "cassidy-claude-3-7-sonnet-thinking",
    "messages": [
      {
        "role": "user",
        "content": [
          {"type": "text", "text": "这张图片里有什么？"},
          {"type": "image_url", "image_url": {"url": "data:image/jpeg;base64,..."}}
        ]
      }
    ],
    "stream": false
  }'
```

## 🔧 配置

### 环境变量
- `PORT` - 服务器端口（默认：8000）

### Cassidy认证
需要在代码中配置有效的Cassidy认证信息：
```typescript
private headers = {
  'Cookie': '__Secure-next-auth.session-token=YOUR_SESSION_TOKEN; x-cassidy-oid=YOUR_ORG_ID; ajs_user_id=YOUR_USER_ID'
};
```

## 📊 响应格式

### 非流式响应
```json
{
  "id": "chatcmpl-xxx",
  "object": "chat.completion",
  "created": 1699999999,
  "model": "cassidy-claude-3-7-sonnet-thinking",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "<think>\n推理过程...\n</think>\n\n实际回复内容"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 0,
    "completion_tokens": 0,
    "total_tokens": 0
  }
}
```

### 流式响应
```
data: {"id":"chatcmpl-xxx","object":"chat.completion.chunk","created":1699999999,"model":"cassidy-claude-3-7-sonnet-thinking","choices":[{"index":0,"delta":{"content":"你好"},"finish_reason":null}]}

data: {"id":"chatcmpl-xxx","object":"chat.completion.chunk","created":1699999999,"model":"cassidy-claude-3-7-sonnet-thinking","choices":[{"index":0,"delta":{"content":"！"},"finish_reason":null}]}

data: {"id":"chatcmpl-xxx","object":"chat.completion.chunk","created":1699999999,"model":"cassidy-claude-3-7-sonnet-thinking","choices":[{"index":0,"delta":{},"finish_reason":"stop"}]}

data: [DONE]
```

## 🧠 思考内容处理

AI的推理过程会被包装在`<think></think>`标签中：

```
<think>
用户问候我，我应该用中文回复，因为他们用中文问候。我需要简单介绍自己...
</think>

你好！我是Claude，由Anthropic开发的AI助手。我可以帮助您处理各种任务...
```

## 🔍 错误处理

### 常见错误码
- `400` - 请求格式错误
- `500` - 服务器内部错误
- `404` - 端点不存在

### 错误响应格式
```json
{
  "error": {
    "message": "错误描述",
    "type": "error_type",
    "code": "error_code"
  }
}
```

## 🚦 限制和注意事项

1. **认证信息** - 需要有效的Cassidy账户和session token
2. **图片上传** - 多模态功能需要先将图片上传到Cassidy获取引用ID
3. **速率限制** - 遵循Cassidy的API使用限制
4. **模型支持** - 目前只支持claude-3-7-sonnet-thinking模型

## 🔧 开发和调试

### 启用详细日志
```bash
DENO_LOG=debug deno run --allow-net --allow-env cassidy-openai-proxy.ts
```

### 自定义配置
修改`CassidyOpenAIProxy`类中的配置：
```typescript
private baseUrl = 'app.cassidyai.com';
private userId = 'YOUR_USER_ID';
private assistantId = 'claude-3-7-sonnet-thinking';
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！
