const https = require('https');
const zlib = require('zlib');
const { v4: uuidv4 } = require('uuid');

/**
 * Cassidy AI API 联动演示脚本
 * 演示发送消息和获取回复的完整流程
 */
class CassidyAPIDemo {
    constructor() {
        this.baseUrl = 'app.cassidyai.com';
        this.chatId = 'cmbkfpy9905cj23bi1ukecu1o';
        this.userId = 'cmbkfclsy05jb5v7vp4wh8s9s';
        this.assistantId = 'claude-3-7-sonnet-thinking';
        
        // 基础请求头
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'x-cassidy-oid': 'cmbkfcw6r05b823bice2rvnpu',
            'origin': 'https://app.cassidyai.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'Cookie': '_reb2buid=03c502eb-1e36-4281-9317-e6ab2fb4db7d-1749191564169; __Secure-next-auth.session-token=5d079c3b-b1aa-4ca8-acdf-b3c85a9f1450; ajs_user_id=cmbkfclsy05jb5v7vp4wh8s9s; x-cassidy-oid=cmbkfcw6r05b823bice2rvnpu'
        };
    }

    // 通用HTTP请求方法（支持gzip解压）
    async makeRequest(options, postData = null) {
        return new Promise((resolve, reject) => {
            const req = https.request(options, (res) => {
                let chunks = [];
                
                res.on('data', (chunk) => {
                    chunks.push(chunk);
                });
                
                res.on('end', () => {
                    let buffer = Buffer.concat(chunks);
                    
                    // 处理gzip压缩
                    if (res.headers['content-encoding'] === 'gzip') {
                        zlib.gunzip(buffer, (err, decompressed) => {
                            if (err) {
                                reject(err);
                                return;
                            }
                            
                            try {
                                const jsonData = JSON.parse(decompressed.toString());
                                resolve({ statusCode: res.statusCode, data: jsonData });
                            } catch (error) {
                                resolve({ statusCode: res.statusCode, data: decompressed.toString() });
                            }
                        });
                    } else {
                        try {
                            const jsonData = JSON.parse(buffer.toString());
                            resolve({ statusCode: res.statusCode, data: jsonData });
                        } catch (error) {
                            resolve({ statusCode: res.statusCode, data: buffer.toString() });
                        }
                    }
                });
            });
            
            req.on('error', reject);
            
            if (postData) {
                req.write(postData);
            }
            
            req.end();
        });
    }

    // API 1: 发送消息
    async sendMessage(message) {
        console.log(`\n🚀 步骤1: 发送消息 "${message}"`);
        
        const messageId = uuidv4();
        const payload = {
            "0": {
                "chatId": this.chatId,
                "userMessage": {
                    "contents": [{ "type": "text", "text": message }],
                    "id": messageId,
                    "nodeType": "StandardUserNode",
                    "createdAt": Date.now(),
                    "examples": [],
                    "sentFromChromePlugin": false,
                    "imageReferenceIds": [],
                    "inReplyToMessageId": null,
                    "sentByUserId": this.userId
                },
                "assistantId": this.assistantId,
                "parameters": {
                    "searchTimeframe": "all",
                    "isWebsearchEnabled": false
                }
            }
        };

        const options = {
            hostname: this.baseUrl,
            port: 443,
            path: '/api/trpc/chat.sendMessage?batch=1',
            method: 'POST',
            headers: this.headers
        };

        const response = await this.makeRequest(options, JSON.stringify(payload));
        console.log(`✅ 发送成功，状态码: ${response.statusCode}`);
        console.log(`📤 响应: ${JSON.stringify(response.data)}`);
        
        return { messageId, response: response.data };
    }

    // API 2: 获取消息列表
    async getMessages() {
        console.log(`\n📥 步骤2: 获取消息列表`);
        
        const input = encodeURIComponent(JSON.stringify({
            "0": { "id": this.chatId }
        }));

        const options = {
            hostname: this.baseUrl,
            port: 443,
            path: `/api/trpc/chat.getMessages?batch=1&input=${input}`,
            method: 'GET',
            headers: this.headers
        };

        const response = await this.makeRequest(options);
        console.log(`✅ 获取成功，状态码: ${response.statusCode}`);
        
        const nodes = response.data[0]?.result?.data?.nodes || [];
        console.log(`📋 共获取到 ${nodes.length} 条消息`);
        
        return response.data;
    }

    // 分析最新的AI回复
    analyzeLatestAIResponse(messages) {
        const nodes = messages[0]?.result?.data?.nodes || [];
        
        // 找到最新的AI回复节点
        const aiNodes = nodes
            .filter(node => node.nodeType === 'ActionAssistantNode')
            .sort((a, b) => b.createdAt - a.createdAt);
        
        if (aiNodes.length === 0) {
            console.log('❌ 未找到AI回复');
            return null;
        }
        
        const latestAI = aiNodes[0];
        console.log(`\n🤖 最新AI回复分析:`);
        console.log(`   ID: ${latestAI.id}`);
        console.log(`   状态: ${latestAI.status}`);
        console.log(`   创建时间: ${new Date(latestAI.createdAt).toLocaleString()}`);
        
        if (latestAI.text && latestAI.text.length > 0) {
            const textContent = latestAI.text
                .filter(t => t.type === 'text')
                .map(t => t.text)
                .join('\n');
            
            if (textContent) {
                console.log(`   回复内容: ${textContent.substring(0, 100)}${textContent.length > 100 ? '...' : ''}`);
            }
        }
        
        return latestAI;
    }

    // 完整的API联动演示
    async demonstrateAPIFlow(message = "你好，请简单介绍一下你自己") {
        console.log('🎯 Cassidy AI API 联动演示');
        console.log('=' .repeat(60));
        
        try {
            // 步骤1: 发送消息
            const sendResult = await this.sendMessage(message);
            
            // 等待一下让服务器处理
            console.log('\n⏳ 等待服务器处理...');
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 步骤2: 获取消息列表
            const messages = await this.getMessages();
            
            // 步骤3: 分析AI回复
            const aiResponse = this.analyzeLatestAIResponse(messages);
            
            console.log('\n' + '=' .repeat(60));
            console.log('✅ API联动演示完成');
            
            return {
                sendResult,
                messages,
                aiResponse
            };
            
        } catch (error) {
            console.error('❌ 演示失败:', error.message);
            throw error;
        }
    }
}

// 使用示例
async function main() {
    const demo = new CassidyAPIDemo();
    
    console.log('Cassidy AI API 联动机制分析:');
    console.log('1. POST /api/trpc/chat.sendMessage - 发送用户消息');
    console.log('2. GET /api/trpc/chat.getMessages - 获取完整对话历史');
    console.log('3. 客户端轮询检查AI回复状态直到完成\n');
    
    await demo.demonstrateAPIFlow();
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = CassidyAPIDemo;
