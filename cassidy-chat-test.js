const https = require('https');
const zlib = require('zlib');
const { v4: uuidv4 } = require('uuid');

class CassidyChatTester {
    constructor() {
        this.baseUrl = 'app.cassidyai.com';
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'sec-ch-ua-platform': '"macOS"',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'x-cassidy-oid': 'cmbkfcw6r05b823bice2rvnpu',
            'origin': 'https://app.cassidyai.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'referer': 'https://app.cassidyai.com/chat/v2/cmbkfpy9905cj23bi1ukecu1o?oid=cmbkfcw6r05b823bice2rvnpu',
            'accept-language': 'zh-CN,zh;q=0.9',
            'priority': 'u=1, i',
            'Cookie': '_reb2buid=03c502eb-1e36-4281-9317-e6ab2fb4db7d-1749191564169; _reb2bsessionID=ASllI0MO0PYeALFod2ePRLtN; __Host-next-auth.csrf-token=881dc6a7dbf891f242b06d9eaf06a38978ed79b1b619c898cd6dde9d1ba51660%7C916f5177206601ceb215a567fdbab3b441dfd281bfd363d91b86c6f737b45ef6; _reb2bgeo=%7B%22city%22%3A%22Tokyo%22%2C%22country%22%3A%22Japan%22%2C%22countryCode%22%3A%22JP%22%2C%22hosting%22%3Atrue%2C%22isp%22%3A%22Google%20LLC%22%2C%22lat%22%3A35.6761%2C%22proxy%22%3Afalse%2C%22region%22%3A%2213%22%2C%22regionName%22%3A%22Tokyo%22%2C%22status%22%3A%22success%22%2C%22timezone%22%3A%22Asia%2FTokyo%22%2C%22zip%22%3A%22%22%7D; _ga_9976ESBG2T=GS2.1.s1749191564$o1$g0$t1749191564$j60$l0$h0; _ga=GA1.1.965129005.1749191565; ph_phc_SkVqEyXKUtuv6LpjFub4Mj3oHf4eeTr1zKweMduxeTR_posthog=%7B%22distinct_id%22%3A%22019743f1-1b51-7e43-8e23-2d0c57363a09%22%2C%22%24sesid%22%3A%5B1749191565327%2C%22019743f1-1b50-7f47-8c36-37c34e116fc1%22%2C1749191564112%5D%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Fwww.cassidyai.com%2F%22%7D%7D; ajs_anonymous_id=124e1089-83b5-472a-862f-bbf809348ff6; intercom-id-n12guz9i=de99175e-ef9c-464f-9883-d2fb192ec640; intercom-device-id-n12guz9i=2e353d2f-d377-4d98-9449-db3b44453d0b; __Secure-next-auth.callback-url=https%3A%2F%2Fapp.cassidyai.com%2Fauth%2Fwelcome; __Secure-next-auth.session-token=5d079c3b-b1aa-4ca8-acdf-b3c85a9f1450; ajs_user_id=cmbkfclsy05jb5v7vp4wh8s9s; ajs_group_id=cmbkfcw6r05b823bice2rvnpu; intercom-session-n12guz9i=K3pualNWUVh3QktReXNhWnJSNjZFTWt0WExhSVNpOWR1QVdnQzFNRlpJazJmZ2gvdEhybS94QXAraFpNSnYvMXpGMlJ3RTFiVzNYKzZjc3ZzNTdFK0UyclhGczVuc0lsU2hBdUozR1BJcWs9LS11WmpzQlBwMWVNNkF6WTkyN21TMmdnPT0=--ae267f8a9e0d3939cabd442754520ee8bf2b5aa9; onboarding-create-chat=true; x-cassidy-oid=cmbkfcw6r05b823bice2rvnpu; hasDismissedReferencesTip=false; selected-assistant-id-cmbkfcw6r05b823bice2rvnpu=%22claude-3-7-sonnet-thinking%22; ph_phc_xBWZKrLRnNN4J4LPrcre65EIzzS7vD2qpMdlBepowfR_posthog=%7B%22distinct_id%22%3A%22cmbkfclsy05jb5v7vp4wh8s9s%22%2C%22%24sesid%22%3A%5B1749192479343%2C%22019743f1-2159-7da8-b1e5-915dcec78f23%22%2C1749191565657%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22https%3A%2F%2Fwww.cassidyai.com%2F%22%2C%22u%22%3A%22https%3A%2F%2Fapp.cassidyai.com%2Fauth%2Fsignin%22%7D%7D'
        };
        
        this.chatId = 'cmbkfpy9905cj23bi1ukecu1o';
        this.userId = 'cmbkfclsy05jb5v7vp4wh8s9s';
        this.assistantId = 'claude-3-7-sonnet-thinking';
    }

    // 发送HTTPS请求的通用方法
    makeRequest(options, postData = null) {
        return new Promise((resolve, reject) => {
            const req = https.request(options, (res) => {
                let chunks = [];

                res.on('data', (chunk) => {
                    chunks.push(chunk);
                });

                res.on('end', () => {
                    let buffer = Buffer.concat(chunks);

                    // 检查是否是gzip压缩
                    if (res.headers['content-encoding'] === 'gzip') {
                        zlib.gunzip(buffer, (err, decompressed) => {
                            if (err) {
                                reject(err);
                                return;
                            }

                            try {
                                const jsonData = JSON.parse(decompressed.toString());
                                resolve({
                                    statusCode: res.statusCode,
                                    headers: res.headers,
                                    data: jsonData
                                });
                            } catch (error) {
                                resolve({
                                    statusCode: res.statusCode,
                                    headers: res.headers,
                                    data: decompressed.toString()
                                });
                            }
                        });
                    } else {
                        // 非压缩数据
                        try {
                            const jsonData = JSON.parse(buffer.toString());
                            resolve({
                                statusCode: res.statusCode,
                                headers: res.headers,
                                data: jsonData
                            });
                        } catch (error) {
                            resolve({
                                statusCode: res.statusCode,
                                headers: res.headers,
                                data: buffer.toString()
                            });
                        }
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            if (postData) {
                req.write(postData);
            }

            req.end();
        });
    }

    // 发送消息
    async sendMessage(message, inReplyToMessageId = null) {
        const messageId = uuidv4();
        const timestamp = Date.now();
        
        const payload = {
            "0": {
                "chatId": this.chatId,
                "userMessage": {
                    "contents": [
                        {
                            "type": "text",
                            "text": message
                        }
                    ],
                    "id": messageId,
                    "nodeType": "StandardUserNode",
                    "createdAt": timestamp,
                    "examples": [],
                    "sentFromChromePlugin": false,
                    "imageReferenceIds": [],
                    "inReplyToMessageId": inReplyToMessageId,
                    "sentByUserId": this.userId
                },
                "assistantId": this.assistantId,
                "parameters": {
                    "searchTimeframe": "all",
                    "isWebsearchEnabled": false
                }
            }
        };

        const options = {
            hostname: this.baseUrl,
            port: 443,
            path: '/api/trpc/chat.sendMessage?batch=1',
            method: 'POST',
            headers: this.headers
        };

        console.log('🚀 发送消息:', message);
        const response = await this.makeRequest(options, JSON.stringify(payload));
        console.log('📤 发送消息响应:', JSON.stringify(response.data, null, 2));
        
        return {
            messageId,
            response: response.data
        };
    }

    // 获取消息列表
    async getMessages() {
        const input = encodeURIComponent(JSON.stringify({
            "0": {
                "id": this.chatId
            }
        }));

        const options = {
            hostname: this.baseUrl,
            port: 443,
            path: `/api/trpc/chat.getMessages?batch=1&input=${input}`,
            method: 'GET',
            headers: this.headers
        };

        console.log('📥 获取消息列表...');
        const response = await this.makeRequest(options);
        console.log('📋 消息列表响应:', JSON.stringify(response.data, null, 2));
        
        return response.data;
    }

    // 等待AI回复完成
    async waitForAIResponse(maxAttempts = 10, interval = 2000) {
        for (let i = 0; i < maxAttempts; i++) {
            console.log(`⏳ 等待AI回复... (尝试 ${i + 1}/${maxAttempts})`);
            
            const messages = await this.getMessages();
            const nodes = messages[0]?.result?.data?.nodes || [];
            
            // 查找最新的AI回复节点
            const latestAINode = nodes
                .filter(node => node.nodeType === 'ActionAssistantNode')
                .sort((a, b) => b.createdAt - a.createdAt)[0];
            
            if (latestAINode && latestAINode.status === 'Finished') {
                console.log('✅ AI回复完成!');
                return latestAINode;
            }
            
            if (i < maxAttempts - 1) {
                await new Promise(resolve => setTimeout(resolve, interval));
            }
        }
        
        console.log('⚠️ 等待AI回复超时');
        return null;
    }

    // 完整的对话测试流程
    async testChatFlow(message = "你好") {
        try {
            console.log('🎯 开始测试Cassidy聊天流程');
            console.log('=' .repeat(50));
            
            // 1. 发送消息
            const sendResult = await this.sendMessage(message);
            
            // 2. 等待一下让服务器处理
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // 3. 获取消息列表（包含AI回复）
            const messages = await this.getMessages();
            
            // 4. 等待AI回复完成
            const aiResponse = await this.waitForAIResponse();
            
            if (aiResponse) {
                console.log('🤖 AI最终回复:');
                console.log(aiResponse.text.map(t => t.text).join('\n'));
            }
            
            console.log('=' .repeat(50));
            console.log('✅ 测试完成');
            
            return {
                sendResult,
                messages,
                aiResponse
            };
            
        } catch (error) {
            console.error('❌ 测试失败:', error);
            throw error;
        }
    }
}

// 使用示例
async function main() {
    const tester = new CassidyChatTester();
    
    // 测试发送消息并获取回复
    await tester.testChatFlow("你好，请介绍一下你自己");
    
    // 可以测试多轮对话
    // await tester.testChatFlow("请解释一下机器学习的基本概念");
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(console.error);
}

module.exports = CassidyChatTester;
