#!/usr/bin/env deno run --allow-net --allow-env

/**
 * <PERSON> AI to OpenAI API Proxy
 * 将OpenAI v1对话接口转换为Cassidy AI接口
 * 支持流式/非流式/多模态响应
 */

/// <reference types="https://deno.land/x/types/index.d.ts" />

interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string | Array<{
    type: 'text' | 'image_url';
    text?: string;
    image_url?: { url: string };
  }>;
}

interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  stream?: boolean;
  temperature?: number;
  max_tokens?: number;
}

interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message?: {
      role: string;
      content: string;
    };
    delta?: {
      role?: string;
      content?: string;
    };
    finish_reason: string | null;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

class CassidyOpenAIProxy {
  private baseUrl = 'app.cassidyai.com';
  private userId = 'cmbkfclsy05jb5v7vp4wh8s9s';
  private assistantId = 'claude-3-7-sonnet-thinking';
  
  private headers = {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    'Content-Type': 'application/json',
    'x-cassidy-oid': 'cmbkfcw6r05b823bice2rvnpu',
    'origin': 'https://app.cassidyai.com',
    'Cookie': '__Secure-next-auth.session-token=5d079c3b-b1aa-4ca8-acdf-b3c85a9f1450; x-cassidy-oid=cmbkfcw6r05b823bice2rvnpu; ajs_user_id=cmbkfclsy05jb5v7vp4wh8s9s'
  };

  // 创建新聊天
  async createNewChat(): Promise<string> {
    const response = await fetch(`https://${this.baseUrl}/api/trpc/chat.createNewChat?batch=1`, {
      method: 'POST',
      headers: this.headers,
      body: '{}'
    });

    if (!response.ok) {
      throw new Error(`Failed to create chat: ${response.status}`);
    }

    const data = await response.json();
    const chatId = data[0]?.result?.data?.chat?.id;
    
    if (!chatId) {
      throw new Error('No chat ID returned');
    }

    return chatId;
  }

  // 发送消息到Cassidy
  async sendMessage(chatId: string, content: string, imageReferenceIds: string[] = []): Promise<void> {
    const messageId = crypto.randomUUID();
    
    const payload = {
      "0": {
        "chatId": chatId,
        "userMessage": {
          "contents": [{ "type": "text", "text": content }],
          "id": messageId,
          "nodeType": "StandardUserNode",
          "createdAt": Date.now(),
          "examples": [],
          "sentFromChromePlugin": false,
          "imageReferenceIds": imageReferenceIds,
          "inReplyToMessageId": null,
          "sentByUserId": this.userId
        },
        "assistantId": this.assistantId,
        "parameters": {
          "searchTimeframe": "all",
          "isWebsearchEnabled": false
        }
      }
    };

    const response = await fetch(`https://${this.baseUrl}/api/trpc/chat.sendMessage?batch=1`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`Failed to send message: ${response.status}`);
    }
  }

  // 订阅SSE事件流
  async subscribeToEvents(chatId: string): Promise<ReadableStream<Uint8Array>> {
    const input = encodeURIComponent(JSON.stringify({ chatId }));
    
    const response = await fetch(
      `https://${this.baseUrl}/api/trpc/chat.subscribeToRealtimeChatEvents?input=${input}`,
      {
        headers: {
          ...this.headers,
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to subscribe to events: ${response.status}`);
    }

    return response.body!;
  }

  // 转换OpenAI消息为文本内容
  private convertMessagesToText(messages: OpenAIMessage[]): { text: string; imageReferenceIds: string[] } {
    let text = '';
    const imageReferenceIds: string[] = [];

    for (const message of messages) {
      if (message.role === 'system') {
        text += `System: ${typeof message.content === 'string' ? message.content : message.content.map(c => c.text || '[Image]').join('')}\n\n`;
      } else if (message.role === 'user') {
        if (typeof message.content === 'string') {
          text += message.content;
        } else {
          for (const content of message.content) {
            if (content.type === 'text') {
              text += content.text || '';
            } else if (content.type === 'image_url') {
              text += '[Image uploaded]';
              // 注意：实际使用时需要上传图片到Cassidy并获取引用ID
              // imageReferenceIds.push(uploadedImageId);
            }
          }
        }
      }
    }

    return { text: text.trim(), imageReferenceIds };
  }

  // 解析SSE流并转换为OpenAI格式
  async *parseSSEStream(stream: ReadableStream<Uint8Array>, requestId: string): AsyncGenerator<string> {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let currentContent = '';
    let thinkingContent = '';
    let isFinished = false;
    let thinkingSent = false;

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ') && line.length > 6) {
            try {
              const data = JSON.parse(line.substring(6));

              if (data.type === 'chatMessagePatch') {
                const patches = data.message.patch;

                for (const patch of patches) {
                  if (patch.op === 'add' && patch.path === '/text/0') {
                    // 开始思考内容
                    if (!thinkingSent) {
                      const thinkStartChunk: OpenAIResponse = {
                        id: requestId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: 'cassidy-claude-3-7-sonnet-thinking',
                        choices: [{
                          index: 0,
                          delta: { content: '<think>\n' },
                          finish_reason: null
                        }]
                      };
                      yield `data: ${JSON.stringify(thinkStartChunk)}\n\n`;

                      // 发送初始思考内容
                      const initialThinking = patch.value?.text || '';
                      if (initialThinking) {
                        thinkingContent = initialThinking;
                        const chunk: OpenAIResponse = {
                          id: requestId,
                          object: 'chat.completion.chunk',
                          created: Math.floor(Date.now() / 1000),
                          model: 'cassidy-claude-3-7-sonnet-thinking',
                          choices: [{
                            index: 0,
                            delta: { content: initialThinking },
                            finish_reason: null
                          }]
                        };
                        yield `data: ${JSON.stringify(chunk)}\n\n`;
                      }
                    }
                  } else if (patch.op === 'replace' && patch.path === '/text/0/text') {
                    // 思考内容流式更新
                    const newThinking = patch.value;
                    const thinkingDelta = newThinking.substring(thinkingContent.length);
                    thinkingContent = newThinking;

                    if (thinkingDelta) {
                      const chunk: OpenAIResponse = {
                        id: requestId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: 'cassidy-claude-3-7-sonnet-thinking',
                        choices: [{
                          index: 0,
                          delta: { content: thinkingDelta },
                          finish_reason: null
                        }]
                      };
                      yield `data: ${JSON.stringify(chunk)}\n\n`;
                    }
                  } else if (patch.op === 'add' && patch.path === '/text/1') {
                    // 思考结束，开始实际回复
                    if (!thinkingSent) {
                      const thinkEndChunk: OpenAIResponse = {
                        id: requestId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: 'cassidy-claude-3-7-sonnet-thinking',
                        choices: [{
                          index: 0,
                          delta: { content: '\n</think>\n\n' },
                          finish_reason: null
                        }]
                      };
                      yield `data: ${JSON.stringify(thinkEndChunk)}\n\n`;
                      thinkingSent = true;
                    }

                    // 发送第一部分实际内容
                    const initialContent = patch.value?.text || '';
                    if (initialContent) {
                      currentContent = initialContent;
                      const chunk: OpenAIResponse = {
                        id: requestId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: 'cassidy-claude-3-7-sonnet-thinking',
                        choices: [{
                          index: 0,
                          delta: { content: initialContent },
                          finish_reason: null
                        }]
                      };
                      yield `data: ${JSON.stringify(chunk)}\n\n`;
                    }
                  } else if (patch.op === 'replace' && patch.path === '/text/1/text') {
                    // 实际回复内容流式更新
                    const newContent = patch.value;
                    const delta = newContent.substring(currentContent.length);
                    currentContent = newContent;

                    if (delta) {
                      const chunk: OpenAIResponse = {
                        id: requestId,
                        object: 'chat.completion.chunk',
                        created: Math.floor(Date.now() / 1000),
                        model: 'cassidy-claude-3-7-sonnet-thinking',
                        choices: [{
                          index: 0,
                          delta: { content: delta },
                          finish_reason: null
                        }]
                      };
                      yield `data: ${JSON.stringify(chunk)}\n\n`;
                    }
                  } else if (patch.op === 'replace' && patch.path === '/status' && patch.value === 'Finished') {
                    isFinished = true;
                  }
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }

        if (isFinished) break;
      }

      // 发送完成标记
      const finalChunk: OpenAIResponse = {
        id: requestId,
        object: 'chat.completion.chunk',
        created: Math.floor(Date.now() / 1000),
        model: 'cassidy-claude-3-7-sonnet-thinking',
        choices: [{
          index: 0,
          delta: {},
          finish_reason: 'stop'
        }]
      };
      yield `data: ${JSON.stringify(finalChunk)}\n\n`;
      yield 'data: [DONE]\n\n`;

    } finally {
      reader.releaseLock();
    }
  }

  // 处理非流式响应
  async getNonStreamingResponse(stream: ReadableStream<Uint8Array>, requestId: string): Promise<OpenAIResponse> {
    const reader = stream.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let finalContent = '';
    let thinkingContent = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ') && line.length > 6) {
            try {
              const data = JSON.parse(line.substring(6));

              if (data.type === 'chatMessagePatch') {
                const patches = data.message.patch;

                for (const patch of patches) {
                  if (patch.op === 'replace' && patch.path === '/text/0/text') {
                    // 思考内容 (reasoning)
                    thinkingContent = patch.value;
                  } else if (patch.op === 'replace' && patch.path === '/text/1/text') {
                    // 实际回复内容
                    finalContent = patch.value;
                  } else if (patch.op === 'add' && patch.path === '/text/1') {
                    // 开始实际回复，获取初始内容
                    finalContent = patch.value?.text || '';
                  } else if (patch.op === 'replace' && patch.path === '/status' && patch.value === 'Finished') {
                    // 构建最终响应，包含思考内容
                    let responseContent = finalContent;
                    if (thinkingContent) {
                      responseContent = `<think>\n${thinkingContent}\n</think>\n\n${finalContent}`;
                    }

                    return {
                      id: requestId,
                      object: 'chat.completion',
                      created: Math.floor(Date.now() / 1000),
                      model: 'cassidy-claude-3-7-sonnet-thinking',
                      choices: [{
                        index: 0,
                        message: {
                          role: 'assistant',
                          content: responseContent
                        },
                        finish_reason: 'stop'
                      }],
                      usage: {
                        prompt_tokens: 0,
                        completion_tokens: 0,
                        total_tokens: 0
                      }
                    };
                  }
                }
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    // 如果没有收到完成信号，返回当前内容
    let responseContent = finalContent;
    if (thinkingContent) {
      responseContent = `<think>\n${thinkingContent}\n</think>\n\n${finalContent}`;
    }

    return {
      id: requestId,
      object: 'chat.completion',
      created: Math.floor(Date.now() / 1000),
      model: 'cassidy-claude-3-7-sonnet-thinking',
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: responseContent || 'No response received'
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0
      }
    };
  }

  // 主要的聊天完成方法
  async chatCompletion(request: OpenAIRequest): Promise<Response> {
    const requestId = `chatcmpl-${crypto.randomUUID()}`;
    
    try {
      // 1. 创建聊天
      const chatId = await this.createNewChat();
      
      // 2. 订阅事件流
      const eventStream = await this.subscribeToEvents(chatId);
      
      // 3. 转换消息并发送
      const { text, imageReferenceIds } = this.convertMessagesToText(request.messages);
      await this.sendMessage(chatId, text, imageReferenceIds);
      
      // 4. 根据是否流式返回不同响应
      if (request.stream) {
        // 流式响应
        const self = this;
        const stream = new ReadableStream({
          async start(controller) {
            try {
              for await (const chunk of self.parseSSEStream(eventStream, requestId)) {
                controller.enqueue(new TextEncoder().encode(chunk));
              }
            } catch (error) {
              console.error('Stream error:', error);
            } finally {
              controller.close();
            }
          }
        });

        return new Response(stream, {
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        });
      } else {
        // 非流式响应
        const response = await this.getNonStreamingResponse(eventStream, requestId);
        
        return new Response(JSON.stringify(response), {
          headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        });
      }
    } catch (error) {
      console.error('Chat completion error:', error);
      
      const errorResponse = {
        error: {
          message: error.message || 'Internal server error',
          type: 'server_error',
          code: 'internal_error'
        }
      };
      
      return new Response(JSON.stringify(errorResponse), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      });
    }
  }
}

// 验证授权
function validateAuth(request: Request): boolean {
  const authKey = Deno.env.get('AUTH_KEY');
  if (!authKey) {
    return true; // 如果没有设置AUTH_KEY，则不验证
  }

  const authorization = request.headers.get('Authorization');
  if (!authorization) {
    return false;
  }

  // 支持 "Bearer token" 或 "token" 格式
  const token = authorization.startsWith('Bearer ')
    ? authorization.substring(7)
    : authorization;

  return token === authKey;
}

// 返回401未授权响应
function unauthorizedResponse(): Response {
  return new Response(JSON.stringify({
    error: {
      message: 'Unauthorized: Invalid or missing AUTH_KEY',
      type: 'authentication_error',
      code: 'unauthorized'
    }
  }), {
    status: 401,
    headers: { 'Content-Type': 'application/json' }
  });
}

// 返回404响应
function notFoundResponse(): Response {
  return new Response(JSON.stringify({
    error: {
      message: 'Not found',
      type: 'invalid_request_error',
      code: 'not_found'
    }
  }), {
    status: 404,
    headers: { 'Content-Type': 'application/json' }
  });
}

// HTTP服务器
async function handleRequest(request: Request): Promise<Response> {
  const url = new URL(request.url);

  // 处理CORS预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
  }

  // 健康检查（不需要验证）
  if (url.pathname === '/health') {
    return new Response(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      auth_required: !!Deno.env.get('AUTH_KEY')
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  // OpenAI兼容的聊天完成端点
  if (url.pathname === '/v1/chat/completions' && request.method === 'POST') {
    // 验证授权
    if (!validateAuth(request)) {
      return unauthorizedResponse();
    }

    try {
      const body = await request.json() as OpenAIRequest;

      // 验证请求
      if (!body.messages || !Array.isArray(body.messages) || body.messages.length === 0) {
        return new Response(JSON.stringify({
          error: {
            message: 'Invalid request: messages array is required',
            type: 'invalid_request_error',
            code: 'invalid_request'
          }
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const proxy = new CassidyOpenAIProxy();
      return await proxy.chatCompletion(body);

    } catch (error) {
      console.error('Request processing error:', error);

      return new Response(JSON.stringify({
        error: {
          message: 'Invalid JSON in request body',
          type: 'invalid_request_error',
          code: 'invalid_json'
        }
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  // 模型列表端点
  if (url.pathname === '/v1/models' && request.method === 'GET') {
    const models = {
      object: 'list',
      data: [
        {
          id: 'cassidy-claude-3-7-sonnet-thinking',
          object: 'model',
          created: Math.floor(Date.now() / 1000),
          owned_by: 'cassidy-ai',
          permission: [],
          root: 'cassidy-claude-3-7-sonnet-thinking',
          parent: null
        }
      ]
    };

    return new Response(JSON.stringify(models), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    });
  }

  // 404 for other paths
  return notFoundResponse();
}

// 启动服务器
async function main() {
  const port = parseInt(Deno.env.get('PORT') || '8000');

  console.log(`🚀 Cassidy OpenAI Proxy Server starting on port ${port}`);
  console.log(`📡 Health check: http://localhost:${port}/health`);
  console.log(`🤖 Chat completions: http://localhost:${port}/v1/chat/completions`);
  console.log(`📋 Models: http://localhost:${port}/v1/models`);
  console.log('');
  console.log('Example usage:');
  console.log(`curl -X POST http://localhost:${port}/v1/chat/completions \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -d '{
    "model": "cassidy-claude-3-7-sonnet-thinking",
    "messages": [{"role": "user", "content": "你好，请介绍一下你自己"}],
    "stream": true
  }'`);

  const server = Deno.serve({ port }, handleRequest);

  // 优雅关闭
  const signals = ['SIGINT', 'SIGTERM'] as const;
  for (const signal of signals) {
    Deno.addSignalListener(signal, () => {
      console.log(`\n📴 Received ${signal}, shutting down gracefully...`);
      server.shutdown();
      Deno.exit(0);
    });
  }

  await server.finished;
}

// 运行服务器
if (import.meta.main) {
  main().catch(console.error);
}
