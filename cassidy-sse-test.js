const https = require('https');

/**
 * 测试Cassidy是否使用Server-Sent Events进行流式通信
 */
class CassidySSETest {
    constructor() {
        // 可能的SSE端点
        this.possibleEndpoints = [
            '/api/stream/chat',
            '/api/sse/chat',
            '/stream/chat',
            '/sse/chat',
            '/api/chat/stream',
            '/api/trpc/chat.stream',
            '/api/v1/chat/stream',
            '/chat/stream'
        ];
        
        this.baseUrl = 'app.cassidyai.com';
        this.chatId = 'cmbkfpy9905cj23bi1ukecu1o';
        
        this.headers = {
            'Accept': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Origin': 'https://app.cassidyai.com',
            'Referer': 'https://app.cassidyai.com/',
            'Cookie': '__Secure-next-auth.session-token=5d079c3b-b1aa-4ca8-acdf-b3c85a9f1450; x-cassidy-oid=cmbkfcw6r05b823bice2rvnpu'
        };
    }

    async testSSEEndpoint(path) {
        return new Promise((resolve) => {
            // 尝试不同的URL格式
            const testUrls = [
                `${path}`,
                `${path}?chatId=${this.chatId}`,
                `${path}/${this.chatId}`,
                `${path}?batch=1&chatId=${this.chatId}`
            ];

            let completed = 0;
            const results = [];

            testUrls.forEach(async (url, index) => {
                const result = await this.makeSSERequest(url);
                results.push({ url, ...result });
                
                completed++;
                if (completed === testUrls.length) {
                    resolve(results);
                }
            });
        });
    }

    async makeSSERequest(path) {
        return new Promise((resolve) => {
            console.log(`🔍 测试 SSE 端点: ${path}`);
            
            const options = {
                hostname: this.baseUrl,
                port: 443,
                path: path,
                method: 'GET',
                headers: this.headers,
                timeout: 5000
            };

            const req = https.request(options, (res) => {
                console.log(`📡 ${path} - 状态码: ${res.statusCode}`);
                console.log(`📋 ${path} - 响应头:`, {
                    'content-type': res.headers['content-type'],
                    'cache-control': res.headers['cache-control'],
                    'connection': res.headers['connection']
                });

                let data = '';
                let isSSE = false;

                // 检查是否是SSE响应
                if (res.headers['content-type']?.includes('text/event-stream')) {
                    isSSE = true;
                    console.log(`✅ ${path} - 检测到 SSE 流!`);
                }

                res.on('data', (chunk) => {
                    data += chunk.toString();
                    
                    if (isSSE) {
                        console.log(`📨 ${path} - SSE 数据:`, chunk.toString());
                    }
                });

                res.on('end', () => {
                    resolve({
                        success: res.statusCode === 200,
                        statusCode: res.statusCode,
                        isSSE: isSSE,
                        contentType: res.headers['content-type'],
                        data: data.substring(0, 200) // 只保留前200字符
                    });
                });

                // 5秒后关闭连接
                setTimeout(() => {
                    req.destroy();
                }, 5000);
            });

            req.on('error', (error) => {
                console.log(`❌ ${path} - 请求失败:`, error.message);
                resolve({
                    success: false,
                    error: error.message,
                    isSSE: false
                });
            });

            req.on('timeout', () => {
                console.log(`⏰ ${path} - 请求超时`);
                req.destroy();
                resolve({
                    success: false,
                    error: 'Timeout',
                    isSSE: false
                });
            });

            req.end();
        });
    }

    async testAllEndpoints() {
        console.log('🎯 测试 Cassidy SSE 端点');
        console.log('=' .repeat(50));
        
        const allResults = [];
        
        for (const endpoint of this.possibleEndpoints) {
            const results = await this.testSSEEndpoint(endpoint);
            allResults.push(...results);
            
            // 等待一下避免过快请求
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('\n📊 测试结果汇总:');
        console.log('=' .repeat(50));
        
        const sseEndpoints = allResults.filter(r => r.isSSE);
        const successfulEndpoints = allResults.filter(r => r.success);
        const errorEndpoints = allResults.filter(r => !r.success);
        
        if (sseEndpoints.length > 0) {
            console.log('🎉 发现 SSE 端点:');
            sseEndpoints.forEach(r => {
                console.log(`   ${r.url} - ${r.statusCode} - ${r.contentType}`);
            });
        } else {
            console.log('❌ 未发现 SSE 端点');
        }
        
        if (successfulEndpoints.length > 0) {
            console.log('\n✅ 响应成功的端点:');
            successfulEndpoints.forEach(r => {
                console.log(`   ${r.url} - ${r.statusCode} - ${r.contentType || 'N/A'}`);
            });
        }
        
        console.log(`\n📈 统计: 总计 ${allResults.length} 个端点，${successfulEndpoints.length} 个成功，${sseEndpoints.length} 个SSE`);
        
        return allResults;
    }
}

// 使用示例
async function main() {
    try {
        const tester = new CassidySSETest();
        await tester.testAllEndpoints();
    } catch (error) {
        console.error('测试失败:', error);
    }
}

if (require.main === module) {
    main();
}

module.exports = CassidySSETest;
