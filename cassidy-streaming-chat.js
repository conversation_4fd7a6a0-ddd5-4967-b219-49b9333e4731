const https = require('https');
const zlib = require('zlib');
const { v4: uuidv4 } = require('uuid');

/**
 * Cassidy AI 完整流式聊天实现
 * 支持创建聊天、SSE订阅、发送消息和实时接收流式回复
 */
class CassidyStreamingChat {
    constructor() {
        this.baseUrl = 'app.cassidyai.com';
        this.userId = 'cmbkfclsy05jb5v7vp4wh8s9s';
        this.assistantId = 'claude-3-7-sonnet-thinking';
        this.chatId = null;
        this.sseConnection = null;
        
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Content-Type': 'application/json',
            'x-cassidy-oid': 'cmbkfcw6r05b823bice2rvnpu',
            'origin': 'https://app.cassidyai.com',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-mode': 'cors',
            'sec-fetch-dest': 'empty',
            'Cookie': '__Secure-next-auth.session-token=5d079c3b-b1aa-4ca8-acdf-b3c85a9f1450; x-cassidy-oid=cmbkfcw6r05b823bice2rvnpu; ajs_user_id=cmbkfclsy05jb5v7vp4wh8s9s'
        };
    }

    // 通用HTTP请求方法
    async makeRequest(options, postData = null) {
        return new Promise((resolve, reject) => {
            const req = https.request(options, (res) => {
                let chunks = [];
                
                res.on('data', (chunk) => {
                    chunks.push(chunk);
                });
                
                res.on('end', () => {
                    let buffer = Buffer.concat(chunks);
                    
                    if (res.headers['content-encoding'] === 'gzip') {
                        zlib.gunzip(buffer, (err, decompressed) => {
                            if (err) {
                                reject(err);
                                return;
                            }
                            
                            try {
                                const jsonData = JSON.parse(decompressed.toString());
                                resolve({ statusCode: res.statusCode, data: jsonData });
                            } catch (error) {
                                resolve({ statusCode: res.statusCode, data: decompressed.toString() });
                            }
                        });
                    } else {
                        try {
                            const jsonData = JSON.parse(buffer.toString());
                            resolve({ statusCode: res.statusCode, data: jsonData });
                        } catch (error) {
                            resolve({ statusCode: res.statusCode, data: buffer.toString() });
                        }
                    }
                });
            });
            
            req.on('error', reject);
            
            if (postData) {
                req.write(postData);
            }
            
            req.end();
        });
    }

    // 1. 创建新聊天
    async createNewChat() {
        console.log('🆕 创建新聊天...');
        
        const options = {
            hostname: this.baseUrl,
            port: 443,
            path: '/api/trpc/chat.createNewChat?batch=1',
            method: 'POST',
            headers: this.headers
        };

        const response = await this.makeRequest(options, '{}');
        
        if (response.statusCode === 200 && response.data[0]?.result?.data?.chat?.id) {
            this.chatId = response.data[0].result.data.chat.id;
            console.log(`✅ 聊天创建成功: ${this.chatId}`);
            return this.chatId;
        } else {
            throw new Error('创建聊天失败');
        }
    }

    // 2. 订阅实时聊天事件 (SSE)
    async subscribeToRealtimeEvents() {
        if (!this.chatId) {
            throw new Error('请先创建聊天');
        }

        console.log('📡 订阅实时聊天事件...');
        
        const input = encodeURIComponent(JSON.stringify({ chatId: this.chatId }));
        
        const options = {
            hostname: this.baseUrl,
            port: 443,
            path: `/api/trpc/chat.subscribeToRealtimeChatEvents?input=${input}`,
            method: 'GET',
            headers: {
                ...this.headers,
                'Accept': 'text/event-stream',
                'Cache-Control': 'no-cache'
            }
        };

        return new Promise((resolve, reject) => {
            const req = https.request(options, (res) => {
                console.log(`✅ SSE连接建立，状态码: ${res.statusCode}`);
                
                if (res.statusCode !== 200) {
                    reject(new Error(`SSE连接失败: ${res.statusCode}`));
                    return;
                }

                this.sseConnection = res;
                
                let buffer = '';
                
                res.on('data', (chunk) => {
                    buffer += chunk.toString();
                    
                    // 处理SSE数据
                    const lines = buffer.split('\n');
                    buffer = lines.pop(); // 保留不完整的行
                    
                    let currentEvent = null;
                    let currentData = '';
                    
                    for (const line of lines) {
                        if (line.startsWith('event: ')) {
                            currentEvent = line.substring(7);
                        } else if (line.startsWith('data: ')) {
                            currentData = line.substring(6);
                            
                            if (currentData) {
                                this.handleSSEMessage(currentEvent, currentData);
                            }
                            
                            currentEvent = null;
                            currentData = '';
                        } else if (line === '') {
                            // 空行表示消息结束
                            if (currentData) {
                                this.handleSSEMessage(currentEvent, currentData);
                            }
                            currentEvent = null;
                            currentData = '';
                        }
                    }
                });

                res.on('end', () => {
                    console.log('🔒 SSE连接关闭');
                });

                res.on('error', (error) => {
                    console.error('❌ SSE连接错误:', error);
                    reject(error);
                });

                resolve();
            });

            req.on('error', reject);
            req.end();
        });
    }

    // 处理SSE消息
    handleSSEMessage(event, data) {
        try {
            if (event === 'connected') {
                const connData = JSON.parse(data);
                console.log('🔗 SSE已连接:', connData);
                return;
            }
            
            if (event === 'ping') {
                console.log('💓 心跳');
                return;
            }
            
            if (!data.trim()) return;
            
            const message = JSON.parse(data);
            
            switch (message.type) {
                case 'chatNode':
                    console.log('📝 新聊天节点:', {
                        id: message.message.id,
                        type: message.message.nodeType,
                        status: message.message.status
                    });
                    break;
                    
                case 'chatMessagePatch':
                    this.handleMessagePatch(message.message);
                    break;
                    
                case 'metaMessage':
                    console.log('📋 元消息:', message.message);
                    break;
                    
                default:
                    console.log('❓ 未知消息类型:', message.type);
            }
            
        } catch (error) {
            console.error('❌ 解析SSE消息失败:', error, 'Data:', data);
        }
    }

    // 处理消息补丁 (流式更新的核心)
    handleMessagePatch(patchMessage) {
        const { messageId, patch } = patchMessage;
        
        for (const operation of patch) {
            if (operation.op === 'replace' && operation.path.includes('/text')) {
                // 这是文本内容的更新
                console.log(`🔄 [${messageId.substring(0, 8)}...] ${operation.value}`);
            } else if (operation.op === 'replace' && operation.path === '/status') {
                console.log(`📊 [${messageId.substring(0, 8)}...] 状态: ${operation.value}`);
            }
        }
    }

    // 3. 发送消息
    async sendMessage(message) {
        if (!this.chatId) {
            throw new Error('请先创建聊天');
        }

        console.log(`\n🚀 发送消息: "${message}"`);
        
        const messageId = uuidv4();
        const payload = {
            "0": {
                "chatId": this.chatId,
                "userMessage": {
                    "contents": [{ "type": "text", "text": message }],
                    "id": messageId,
                    "nodeType": "StandardUserNode",
                    "createdAt": Date.now(),
                    "examples": [],
                    "sentFromChromePlugin": false,
                    "imageReferenceIds": [],
                    "inReplyToMessageId": null,
                    "sentByUserId": this.userId
                },
                "assistantId": this.assistantId,
                "parameters": {
                    "searchTimeframe": "all",
                    "isWebsearchEnabled": false
                }
            }
        };

        const options = {
            hostname: this.baseUrl,
            port: 443,
            path: '/api/trpc/chat.sendMessage?batch=1',
            method: 'POST',
            headers: this.headers
        };

        const response = await this.makeRequest(options, JSON.stringify(payload));
        console.log(`✅ 消息发送成功`);
        
        return { messageId, response: response.data };
    }

    // 关闭SSE连接
    closeSSEConnection() {
        if (this.sseConnection) {
            this.sseConnection.destroy();
            this.sseConnection = null;
            console.log('🔒 SSE连接已关闭');
        }
    }

    // 完整的流式聊天演示
    async demonstrateStreamingChat() {
        console.log('🎯 Cassidy AI 流式聊天演示');
        console.log('=' .repeat(60));
        
        try {
            // 1. 创建聊天
            await this.createNewChat();
            
            // 2. 订阅实时事件
            await this.subscribeToRealtimeEvents();
            
            // 等待连接稳定
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 3. 发送消息
            await this.sendMessage('你好，请简单介绍一下你自己');
            
            // 4. 等待AI回复完成
            console.log('\n⏳ 等待AI流式回复...');
            await new Promise(resolve => setTimeout(resolve, 15000));
            
            console.log('\n' + '=' .repeat(60));
            console.log('✅ 流式聊天演示完成');
            
        } catch (error) {
            console.error('❌ 演示失败:', error);
        } finally {
            this.closeSSEConnection();
        }
    }
}

// 使用示例
async function main() {
    const chat = new CassidyStreamingChat();
    await chat.demonstrateStreamingChat();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = CassidyStreamingChat;
