const WebSocket = require('ws');

/**
 * 测试Cassidy是否使用WebSocket进行流式通信
 */
class CassidyWebSocketTest {
    constructor() {
        // 可能的WebSocket端点
        this.possibleEndpoints = [
            'wss://app.cassidyai.com/ws',
            'wss://app.cassidyai.com/api/ws',
            'wss://app.cassidyai.com/chat/ws',
            'wss://app.cassidyai.com/stream',
            'wss://api.cassidyai.com/ws'
        ];
        
        this.headers = {
            'Origin': 'https://app.cassidyai.com',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Cookie': '__Secure-next-auth.session-token=5d079c3b-b1aa-4ca8-acdf-b3c85a9f1450; x-cassidy-oid=cmbkfcw6r05b823bice2rvnpu'
        };
    }

    async testWebSocketEndpoint(url) {
        return new Promise((resolve) => {
            console.log(`🔍 测试 WebSocket 端点: ${url}`);
            
            const ws = new WebSocket(url, {
                headers: this.headers,
                timeout: 5000
            });

            const timeout = setTimeout(() => {
                ws.terminate();
                resolve({ url, success: false, error: 'Timeout' });
            }, 5000);

            ws.on('open', () => {
                clearTimeout(timeout);
                console.log(`✅ ${url} - 连接成功`);
                
                // 尝试发送测试消息
                ws.send(JSON.stringify({
                    type: 'test',
                    message: 'hello'
                }));
                
                setTimeout(() => {
                    ws.close();
                    resolve({ url, success: true, error: null });
                }, 1000);
            });

            ws.on('message', (data) => {
                console.log(`📨 ${url} - 收到消息:`, data.toString());
            });

            ws.on('error', (error) => {
                clearTimeout(timeout);
                console.log(`❌ ${url} - 连接失败:`, error.message);
                resolve({ url, success: false, error: error.message });
            });

            ws.on('close', (code, reason) => {
                clearTimeout(timeout);
                console.log(`🔒 ${url} - 连接关闭: ${code} ${reason}`);
            });
        });
    }

    async testAllEndpoints() {
        console.log('🎯 测试 Cassidy WebSocket 端点');
        console.log('=' .repeat(50));
        
        const results = [];
        
        for (const endpoint of this.possibleEndpoints) {
            const result = await this.testWebSocketEndpoint(endpoint);
            results.push(result);
            
            // 等待一下避免过快请求
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('\n📊 测试结果汇总:');
        console.log('=' .repeat(50));
        
        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);
        
        if (successful.length > 0) {
            console.log('✅ 成功的端点:');
            successful.forEach(r => console.log(`   ${r.url}`));
        }
        
        if (failed.length > 0) {
            console.log('❌ 失败的端点:');
            failed.forEach(r => console.log(`   ${r.url} - ${r.error}`));
        }
        
        return results;
    }
}

// 使用示例
async function main() {
    try {
        const tester = new CassidyWebSocketTest();
        await tester.testAllEndpoints();
    } catch (error) {
        console.error('测试失败:', error);
    }
}

if (require.main === module) {
    main();
}

module.exports = CassidyWebSocketTest;
