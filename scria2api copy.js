const API_KEY = "sk-yw12342234";

const DEFAULT_HEADERS = {
  "sec-ch-ua": 
    '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
  "sec-ch-ua-mobile": "?0",
  "sec-ch-ua-platform": '"macOS"',
  "user-agent":
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36",
  "accept": "*/*",
  "accept-language": "zh-CN,zh;q=0.9",
  "content-type": "application/json",
  "origin": "https://mcp.scira.ai",
  "referer": "https://mcp.scira.ai/",
  "sec-fetch-dest": "empty",
  "sec-fetch-site": "same-origin",
  "sec-fetch-mode": "cors",
  "priority": "u=1, i",
};

async function handleStreamResponse(response, model) {
  // 设置响应头
  const headers = new Headers({
    "Content-Type": "text/event-stream",
    "Cache-Control": "no-cache",
    Connection: "keep-alive",
    "Access-Control-Allow-Origin": "*",
  });
  try {
    const { readable, writable } = new TransformStream();
    const writer = writable.getWriter();
    const encoder = new TextEncoder();
    const decoder = new TextDecoder("utf-8");

    // 创建响应流
    const stream = response.body;
    let buffer = "";
    let reasoning = false;
    // 处理响应流
    const reader = stream.getReader();
    const sentText = async (text) => {
      const obj = `data: ${JSON.stringify({
        id: crypto.randomUUID(),
        object: "chat.completion.chunk",
        created: new Date().getTime(),
        choices: [
          {
            index: 0,
            delta: {
              content: text,
              role: "assistant",
            },
          },
        ],
      })}\n\n`;
      await writer.write(encoder.encode(obj));
    };
    (async () => {
      try {
        let thinking = false;
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            await writer.write(encoder.encode("data: [DONE]\n\n"));
            await writer.close();
            break;
          }
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split("\n");
          buffer = lines.pop() || "";
          for (const line of lines) {
            if (!line.trim()) continue;
            if (!line.includes(":")) {
              continue;
            }

            const [type, ...rest] = line.split(":");
            const content = rest.join(":");
            const data = JSON.parse(content);

            if (type === "g" && !thinking) {
              thinking = true;
              await sentText("<think>");
            }
            if (type !== "g" && thinking) {
              thinking = false;
              await sentText("</think>");
            }
            if (type === "0" || type === "g") {
              await sentText(data);
            }
          }
        }
      } catch (error) {
        console.error("流处理错误:", error);
        await sentText("data: 流处理错误:"+ error);
        await writer.write(encoder.encode("data: [DONE]\n\n"));
        await writer.close();
      }
    })();

    // 返回新的响应对象
    return new Response(readable, { headers });
  } catch (error) {
    console.error("处理响应错误:", error);
    return new Response("data: [DONE]\n\n", { headers });
  }
}

async function handleNonStreamResponse(response) {
  const headers = new Headers({
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
  });

  try {
    const decoder = new TextDecoder("utf-8");
    const stream = response.body;
    const reader = stream.getReader();
    let fullResponse = "";

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      
      const text = decoder.decode(value, { stream: true });
      const lines = text.split("\n");
      
      for (const line of lines) {
        if (!line.trim() || !line.includes(":")) continue;
        
        const [type, ...rest] = line.split(":");
        try {
          const content = rest.join(":").trim();
          const data = JSON.parse(content);
          
          if (type === "0") {
            fullResponse += data;
          }
        } catch (e) {
          console.error("JSON parse error:", e, "for line:", line);
          continue;
        }
      }
    }

    return new Response(JSON.stringify({
      id: crypto.randomUUID(),
      object: "chat.completion",
      created: new Date().getTime(),
      choices: [{
        index: 0,
        message: {
          role: "assistant",
          content: fullResponse
        }
      }]
    }), { headers });
    
  } catch (error) {
    console.error("处理响应错误:", error);
    throw error;
  }
}

// 验证 API 密钥
function verifyApiKey(request) {
  const authorization = request.headers.get("Authorization");
  if (!authorization) {
    return new Response(JSON.stringify({ error: "Missing API key" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  const apiKey = authorization.replace("Bearer ", "").trim();
  if (apiKey !== API_KEY) {
    return new Response(JSON.stringify({ error: "Invalid API key" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  return null;
}

async function handleRequest(request) {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  const url = new URL(request.url);

  // 验证 API 密钥（除了 OPTIONS 请求）
  const authError = verifyApiKey(request);
  if (authError) return authError;

  if (request.method === "GET" && url.pathname === "/v1/models") {
    return new Response(
      JSON.stringify({
        object: "list",
        data: [
          {
            id: "claude-3-7-sonnet",
            object: "model",
            created: 1686935002,
            owned_by: "scira",
          }
        ],
      }),
      {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }

  if (request.method === "POST" && url.pathname === "/v1/chat/completions") {
    const body = await request.json();
    try {
      // Transform OpenAI format to Scira format
      // let group = "chat";
      // if(body.model.indexOf('-search') != -1){
      //   group = "web"
      //   body.model = body.model.replace('-search','');
      // }
      // if(body.model == "scira-grok-3"){
      //   body.model = "scira-default";
      // }
      const transformedBody = {
        ...body,
        userId: "hjjgbjbh",
        selectedModel: body.model,
        messages: body.messages.map(msg => {
          if(!msg.content){
            msg.content = "";
          }
          if(msg.tool_calls){
            delete msg.tool_calls;
          }
          
          // 处理消息中的附件（新格式）
          if (msg.experimental_attachments && msg.experimental_attachments.length > 0) {
            return {
              role: msg.role === "system" ? "user" : msg.role,
              content: msg.content,
              experimental_attachments: msg.experimental_attachments
            };
          }
          
          // 处理旧格式的图片（通过content数组）
          if (Array.isArray(msg.content)) {
            const textContent = msg.content.find(c => c.type === "text")?.text || "";
            const attachments = msg.content
              .filter(c => c.type === "image_url")
              .map(img => ({
                name: new URL(img.image_url.url).pathname.split("/").pop(),
                contentType: "image/jpeg", // Assuming JPEG, adjust if needed
                url: img.image_url.url,
                size: 0 // Size unknown from URL
              }));

            return {
              role: msg.role === "system" ? "user" : msg.role,
              content: textContent,
              ...(attachments.length > 0 && { experimental_attachments: attachments })
            };
          }
          
          // 处理普通文本消息
          return {
            role: msg.role === "system" ? "user" : msg.role,
            content: msg.content
          };
        }),
      };
      
      // 更新API请求地址
      const response = await fetch("https://mcp.scira.ai/api/chat", {
        method: "POST",
        headers: {
          ...DEFAULT_HEADERS,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(transformedBody),
      });
      
      // 根据 stream 参数选择处理方式
      return body.stream 
        ? await handleStreamResponse(response, body.model)
        : await handleNonStreamResponse(response);
      
    } catch (error) {
      return new Response(
        JSON.stringify({
          error: {
            message: `${error.message}`,
            type: "server_error",
            param: null,
            code: error.code || null,
          },
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }
  }

  return new Response("Not Found", { status: 404 });
}


// Workers 入口点
export default {
  async fetch(request, env, ctx) {
    return handleRequest(request);
  },
};