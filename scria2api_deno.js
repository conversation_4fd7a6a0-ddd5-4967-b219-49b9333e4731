// Deno 版本的 Scria API 代理服务器
// 使用方法: deno run --allow-net --allow-env scria2api_deno.js

// 配置
const API_KEY = Deno.env.get("API_KEY") || "your-api-key-here";
const PORT = parseInt(Deno.env.get("PORT") || "8000");

// 可用模型映射
const AVAILABLE_MODELS = {
  "scira-claude-3.5-sonnet": "claude 3.5 sonnet",
  "scira-claude-3.5-haiku": "claude 3.5 haiku",
  "scira-gpt-4o": "gpt 4o",
  "scira-gpt-4o-mini": "gpt 4o mini",
  "scira-gemini-2-flash": "gemini 2.0 flash",
  "scira-gemini-1.5-pro": "gemini 1.5 pro",
  "scira-gemini-1.5-flash": "gemini 1.5 flash",
  "scira-deepseek-v3": "deepseek v3",
  "scira-qwen-2.5-coder": "qwen 2.5 coder",
  "scira-llama-3.3": "llama 3.3",
  "scira-gpt-4o-vision": "gpt 4o vision",
  "scira-gemini-2-flash-vision": "gemini 2.0 flash vision",
  "scira-google-pro": "gemini 2.5 pro",
  "scira-llama-4": "llama 4 Maverick",
};

// 生成随机浏览器指纹
function generateBrowserFingerprint() {
  const platforms = ["Windows", "macOS", "Linux"];
  const chromeVersions = ["120", "121", "122", "123", "124", "125"];
  const languages = ["en-US,en;q=0.9", "zh-CN,zh;q=0.9", "en-GB,en;q=0.9"];

  const platform = platforms[Math.floor(Math.random() * platforms.length)];
  const version = chromeVersions[Math.floor(Math.random() * chromeVersions.length)];
  const language = languages[Math.floor(Math.random() * languages.length)];

  return {
    platform,
    version,
    language,
    userAgent: `Mozilla/5.0 (${platform === "Windows" ? "Windows NT 10.0; Win64; x64" :
                 platform === "macOS" ? "Macintosh; Intel Mac OS X 10_15_7" :
                 "X11; Linux x86_64"}) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${version}.0.0.0 Safari/537.36`,
    secChUa: `"Not)A;Brand";v="99", "Google Chrome";v="${version}", "Chromium";v="${version}"`,
  };
}

// 全局浏览器指纹
let currentFingerprint = generateBrowserFingerprint();

function getDefaultHeaders() {
  return {
    "Accept": "text/event-stream,application/json,*/*",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": currentFingerprint.language,
    "Content-Type": "application/json",
    "Origin": "https://scira.ai",
    "Referer": "https://scira.ai/",
    "Sec-CH-UA": currentFingerprint.secChUa,
    "Sec-CH-UA-Mobile": "?0",
    "Sec-CH-UA-Platform": `"${currentFingerprint.platform}"`,
    "User-Agent": currentFingerprint.userAgent,
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin"
  };
}

// Scira 内容提取器
function sciraExtractor(chunk) {
  if (typeof chunk === 'string') {
    const match = chunk.match(/0:"(.*?)"(?=,|$)/);
    if (match) {
      try {
        let content = match[1];
        content = content.replace(/\\u([0-9a-fA-F]{4})/g, (_, code) => {
          return String.fromCharCode(parseInt(code, 16));
        });
        content = content.replace(/\\\\/g, '\\').replace(/\\"/g, '"');
        content = content.replace(/\\n/g, '\n').replace(/\\r/g, '\r').replace(/\\t/g, '\t');
        return content;
      } catch (e) {
        console.error('Content extraction error:', e);
        return null;
      }
    }
  }
  return null;
}

// 刷新浏览器身份
function refreshIdentity() {
  const oldFingerprint = currentFingerprint;
  currentFingerprint = generateBrowserFingerprint();
  console.log('Browser identity refreshed:');
  console.log('  Old User-Agent:', oldFingerprint.userAgent);
  console.log('  New User-Agent:', currentFingerprint.userAgent);
  console.log('  Old Platform:', oldFingerprint.platform);
  console.log('  New Platform:', currentFingerprint.platform);
  console.log('  Old Language:', oldFingerprint.language);
  console.log('  New Language:', currentFingerprint.language);
}

// 处理和合并 system 消息
function processSystemMessages(messages) {
  const systemMessages = messages.filter(msg => msg.role === "system");
  const nonSystemMessages = messages.filter(msg => msg.role !== "system");

  if (systemMessages.length === 0) {
    return messages;
  }

  if (systemMessages.length === 1) {
    return messages;
  }

  const systemContents = systemMessages
    .map(msg => (msg.content || "").trim())
    .filter(content => content.length > 0);

  const uniqueContents = [...new Set(systemContents)];

  if (uniqueContents.length <= 1) {
    const mergedContent = uniqueContents[0] || "";
    return [
      { role: "system", content: mergedContent },
      ...nonSystemMessages
    ];
  }

  const mergedContent = uniqueContents.join('\n\n');

  return [
    { role: "system", content: mergedContent },
    ...nonSystemMessages
  ];
}

// 生成唯一ID (Deno 版本)
function generateUUID() {
  return crypto.randomUUID();
}

// 处理流式响应
async function handleStreamResponse(response, stream) {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder("utf-8");
  
  let buffer = "";
  
  const sentText = async (text) => {
    const obj = `data: ${JSON.stringify({
      id: generateUUID(),
      object: "chat.completion.chunk",
      created: Math.floor(Date.now() / 1000),
      choices: [
        {
          index: 0,
          delta: {
            content: text,
            role: "assistant",
          },
        },
      ],
    })}\n\n`;
    await stream.write(encoder.encode(obj));
  };

  try {
    let thinking = false;
    const reader = response.body?.getReader();
    if (!reader) throw new Error("No response body");

    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        if (thinking) {
          await sentText("</think>");
        }
        await stream.write(encoder.encode("data: [DONE]\n\n"));
        break;
      }

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split("\n");
      buffer = lines.pop() || "";

      for (const line of lines) {
        if (!line.trim()) continue;

        if (line.startsWith('g:')) {
          if (!thinking) {
            thinking = true;
            await sentText("<think>");
          }
          try {
            const content = line.substring(2);
            const data = JSON.parse(content);
            await sentText(data);
          } catch (e) {
            console.error('G-type parsing error:', e);
          }
          continue;
        }

        if (thinking && !line.startsWith('g:')) {
          thinking = false;
          await sentText("</think>");
        }

        const extractedContent = sciraExtractor(line);
        if (extractedContent) {
          await sentText(extractedContent);
        }
      }
    }
  } catch (error) {
    console.error("流处理错误:", error);
    await sentText(`\n\n[错误: ${error.message}]`);
    await stream.write(encoder.encode("data: [DONE]\n\n"));
  }
}

// 处理非流式响应
async function handleNonStreamResponse(response) {
  const decoder = new TextDecoder("utf-8");
  let fullResponse = "";
  let buffer = "";

  const reader = response.body?.getReader();
  if (!reader) throw new Error("No response body");

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    buffer += decoder.decode(value, { stream: true });
    const lines = buffer.split("\n");
    buffer = lines.pop() || "";

    for (const line of lines) {
      if (!line.trim()) continue;
      const extractedContent = sciraExtractor(line);
      if (extractedContent) {
        fullResponse += extractedContent;
      }
    }
  }

  if (buffer.trim()) {
    const extractedContent = sciraExtractor(buffer);
    if (extractedContent) {
      fullResponse += extractedContent;
    }
  }

  return {
    id: generateUUID(),
    object: "chat.completion",
    created: Math.floor(Date.now() / 1000),
    choices: [{
      index: 0,
      message: {
        role: "assistant",
        content: fullResponse
      },
      finish_reason: "stop"
    }],
    usage: {
      prompt_tokens: 0,
      completion_tokens: 0,
      total_tokens: 0
    }
  };
}

// 验证 API 密钥
function verifyApiKey(request) {
  const authorization = request.headers.get("Authorization");
  if (!authorization) {
    return new Response(JSON.stringify({ error: "Missing API key" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  const apiKey = authorization.replace("Bearer ", "").trim();
  if (apiKey !== API_KEY) {
    return new Response(JSON.stringify({ error: "Invalid API key" }), {
      status: 401,
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
      },
    });
  }

  return null;
}

// 主要请求处理函数
async function handleRequest(request) {
  // 处理 CORS 预检请求
  if (request.method === "OPTIONS") {
    return new Response(null, {
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
      },
    });
  }

  const url = new URL(request.url);

  // 验证 API 密钥（除了 OPTIONS 请求）
  const authError = verifyApiKey(request);
  if (authError) return authError;

  // 处理模型列表请求
  if (request.method === "GET" && url.pathname === "/v1/models") {
    const modelList = Object.keys(AVAILABLE_MODELS).map(modelId => ({
      id: modelId,
      object: "model",
      created: 1686935002,
      owned_by: "scira",
      description: AVAILABLE_MODELS[modelId]
    }));

    const searchModels = Object.keys(AVAILABLE_MODELS)
      .filter(modelId => !modelId.includes('vision'))
      .map(modelId => ({
        id: `${modelId}-search`,
        object: "model",
        created: 1686935002,
        owned_by: "scira",
        description: `${AVAILABLE_MODELS[modelId]} (with web search)`
      }));

    return new Response(
      JSON.stringify({
        object: "list",
        data: [...modelList, ...searchModels],
      }),
      {
        headers: {
          "Content-Type": "application/json",
          "Access-Control-Allow-Origin": "*",
        },
      }
    );
  }

  // 处理聊天完成请求
  if (request.method === "POST" && url.pathname === "/v1/chat/completions") {
    const body = await request.json();

    try {
      // 验证模型
      let modelName = body.model;
      let group = "chat";

      // 处理搜索模式
      if (modelName.endsWith('-search')) {
        group = "web";
        modelName = modelName.replace('-search', '');
      }

      // 验证模型是否存在
      if (!AVAILABLE_MODELS[modelName]) {
        return new Response(
          JSON.stringify({
            error: {
              message: `Model '${body.model}' not found. Available models: ${Object.keys(AVAILABLE_MODELS).join(', ')}`,
              type: "invalid_request_error",
              param: "model",
              code: "model_not_found",
            },
          }),
          {
            status: 400,
            headers: {
              "Content-Type": "application/json",
              "Access-Control-Allow-Origin": "*",
            },
          }
        );
      }

      // 生成唯一ID
      const chatId = generateUUID();
      const userId = `user_${generateUUID().substring(0, 8).toUpperCase()}`;

      // 处理和合并 system 消息
      const normalizedMessages = processSystemMessages(body.messages);

      // 处理消息格式
      const processedMessages = normalizedMessages.map(msg => {
        if (msg.role === "system") {
          return {
            role: "system",
            content: msg.content || ""
          };
        }

        const processedMsg = {
          role: msg.role,
          content: msg.content || "",
          parts: [{ type: "text", text: msg.content || "" }]
        };

        // 处理附件
        if (msg.experimental_attachments && msg.experimental_attachments.length > 0) {
          processedMsg.experimental_attachments = msg.experimental_attachments;
        }

        // 处理旧格式的图片
        if (Array.isArray(msg.content)) {
          const textContent = msg.content.find(c => c.type === "text")?.text || "";
          const attachments = msg.content
            .filter(c => c.type === "image_url")
            .map(img => ({
              name: new URL(img.image_url.url).pathname.split("/").pop() || "image.jpg",
              contentType: "image/jpeg",
              url: img.image_url.url,
              size: 0
            }));

          processedMsg.content = textContent;
          processedMsg.parts = [{ type: "text", text: textContent }];
          if (attachments.length > 0) {
            processedMsg.experimental_attachments = attachments;
          }
        }

        return processedMsg;
      });

      // 构建请求负载
      const transformedBody = {
        id: chatId,
        messages: processedMessages,
        model: modelName,
        group: group,
        user_id: userId,
        timezone: "Asia/Shanghai"
      };

      // 发送请求，包含错误处理和重试机制
      let response;
      let retryCount = 0;
      const maxRetries = 2;

      while (retryCount <= maxRetries) {
        try {
          response = await fetch("https://scira.ai/api/search", {
            method: "POST",
            headers: getDefaultHeaders(),
            body: JSON.stringify(transformedBody),
          });

          // 检查响应状态
          if (response.status === 403 || response.status === 429) {
            if (retryCount < maxRetries) {
              console.log(`Received status ${response.status}, refreshing identity and retrying (attempt ${retryCount + 1}/${maxRetries})...`);
              refreshIdentity();
              console.log('Identity refreshed, will use new headers in next request');
              retryCount++;
              continue;
            }
          }

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }

          break;
        } catch (error) {
          if (retryCount >= maxRetries) {
            throw error;
          }
          retryCount++;
          console.log(`Request failed, retrying (${retryCount}/${maxRetries}):`, error.message);
        }
      }

      // 根据 stream 参数选择处理方式
      if (body.stream) {
        // 流式响应
        const readable = new ReadableStream({
          async start(controller) {
            await handleStreamResponse(response, {
              write: (chunk) => controller.enqueue(chunk)
            });
            controller.close();
          }
        });

        return new Response(readable, {
          headers: {
            "Content-Type": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
          },
        });
      } else {
        // 非流式响应
        const result = await handleNonStreamResponse(response);
        return new Response(JSON.stringify(result), {
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        });
      }

    } catch (error) {
      console.error("Request processing error:", error);
      return new Response(
        JSON.stringify({
          error: {
            message: error.message || "Internal server error",
            type: "server_error",
            param: null,
            code: error.code || "internal_error",
          },
        }),
        {
          status: 500,
          headers: {
            "Content-Type": "application/json",
            "Access-Control-Allow-Origin": "*",
          },
        }
      );
    }
  }

  return new Response("Not Found", { status: 404 });
}

// 启动 HTTP 服务器
async function startServer() {
  console.log(`🚀 Scria API Proxy Server starting on port ${PORT}`);
  console.log(`📝 API Key: ${API_KEY === "your-api-key-here" ? "⚠️  Please set API_KEY environment variable" : "✅ Configured"}`);
  console.log(`🌐 Server URL: http://localhost:${PORT}`);
  console.log(`📚 Available endpoints:`);
  console.log(`   GET  /v1/models - List available models`);
  console.log(`   POST /v1/chat/completions - Chat completions`);
  console.log(`\n🔧 Environment variables:`);
  console.log(`   API_KEY - Your API key (required)`);
  console.log(`   PORT - Server port (default: 8000)`);
  console.log(`\n📖 Usage example:`);
  console.log(`   curl -X POST http://localhost:${PORT}/v1/chat/completions \\`);
  console.log(`     -H "Content-Type: application/json" \\`);
  console.log(`     -H "Authorization: Bearer ${API_KEY}" \\`);
  console.log(`     -d '{"model":"scira-claude-3.5-sonnet","messages":[{"role":"user","content":"Hello!"}]}'`);
  console.log(`\n🎯 Starting server...`);

  const server = Deno.serve({
    port: PORT,
    hostname: "0.0.0.0",
  }, handleRequest);

  console.log(`✅ Server is running on http://localhost:${PORT}`);

  // 优雅关闭处理
  const signals = ["SIGINT", "SIGTERM"];
  for (const signal of signals) {
    Deno.addSignalListener(signal, () => {
      console.log(`\n🛑 Received ${signal}, shutting down gracefully...`);
      server.shutdown();
      Deno.exit(0);
    });
  }

  return server;
}

// 主函数
if (import.meta.main) {
  try {
    await startServer();
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    Deno.exit(1);
  }
}
