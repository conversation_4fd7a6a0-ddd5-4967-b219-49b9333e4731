#!/usr/bin/env deno run --allow-net

/**
 * Cassidy OpenAI Proxy 测试脚本
 */

const PROXY_URL = 'http://localhost:8000';
const AUTH_KEY = Deno.env.get('AUTH_KEY');

interface TestCase {
  name: string;
  request: any;
  expectStream?: boolean;
}

const testCases: TestCase[] = [
  {
    name: '简单对话 - 非流式',
    request: {
      model: 'cassidy-claude-3-7-sonnet-thinking',
      messages: [
        { role: 'user', content: '你好，请简单介绍一下你自己' }
      ],
      stream: false
    }
  },
  {
    name: '简单对话 - 流式',
    request: {
      model: 'cassidy-claude-3-7-sonnet-thinking',
      messages: [
        { role: 'user', content: '请用一句话介绍什么是人工智能' }
      ],
      stream: true
    },
    expectStream: true
  },
  {
    name: '多轮对话',
    request: {
      model: 'cassidy-claude-3-7-sonnet-thinking',
      messages: [
        { role: 'system', content: '你是一个有用的AI助手，请用中文回答问题。' },
        { role: 'user', content: '什么是机器学习？' },
        { role: 'assistant', content: '机器学习是人工智能的一个分支...' },
        { role: 'user', content: '能举个具体例子吗？' }
      ],
      stream: false
    }
  }
];

async function testHealthCheck() {
  console.log('🏥 测试健康检查...');
  
  try {
    const response = await fetch(`${PROXY_URL}/health`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 健康检查通过:', data);
    } else {
      console.log('❌ 健康检查失败:', response.status, data);
    }
  } catch (error) {
    console.log('❌ 健康检查错误:', error.message);
  }
  
  console.log('');
}

async function testModels() {
  console.log('📋 测试模型列表...');
  
  try {
    const response = await fetch(`${PROXY_URL}/v1/models`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ 模型列表获取成功:');
      console.log(JSON.stringify(data, null, 2));
    } else {
      console.log('❌ 模型列表获取失败:', response.status, data);
    }
  } catch (error) {
    console.log('❌ 模型列表错误:', error.message);
  }
  
  console.log('');
}

async function testChatCompletion(testCase: TestCase) {
  console.log(`🤖 测试: ${testCase.name}`);
  console.log('请求:', JSON.stringify(testCase.request, null, 2));

  try {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // 如果设置了AUTH_KEY，添加到请求头
    if (AUTH_KEY) {
      headers['Authorization'] = `Bearer ${AUTH_KEY}`;
    }

    const response = await fetch(`${PROXY_URL}/v1/chat/completions`, {
      method: 'POST',
      headers,
      body: JSON.stringify(testCase.request)
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      console.log('❌ 请求失败:', response.status, errorData);
      return;
    }
    
    if (testCase.expectStream) {
      console.log('📡 流式响应:');
      
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (!reader) {
        console.log('❌ 无法获取流式响应');
        return;
      }
      
      let buffer = '';
      let chunkCount = 0;
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';
          
          for (const line of lines) {
            if (line.startsWith('data: ') && line !== 'data: [DONE]') {
              try {
                const data = JSON.parse(line.substring(6));
                const content = data.choices?.[0]?.delta?.content;
                if (content) {
                  process.stdout.write(content);
                  chunkCount++;
                }
              } catch (e) {
                // 忽略解析错误
              }
            } else if (line === 'data: [DONE]') {
              console.log(`\n✅ 流式响应完成 (${chunkCount} 个数据块)`);
              break;
            }
          }
        }
      } finally {
        reader.releaseLock();
      }
    } else {
      const data = await response.json();
      console.log('✅ 非流式响应:');
      console.log('内容:', data.choices?.[0]?.message?.content || '无内容');
      console.log('完整响应:', JSON.stringify(data, null, 2));
    }
    
  } catch (error) {
    console.log('❌ 测试错误:', error.message);
  }
  
  console.log('');
  console.log('-'.repeat(80));
  console.log('');
}

async function runAllTests() {
  console.log('🎯 Cassidy OpenAI Proxy 测试开始');
  console.log('='.repeat(80));
  console.log('');
  
  // 健康检查
  await testHealthCheck();
  
  // 模型列表
  await testModels();
  
  // 聊天完成测试
  for (const testCase of testCases) {
    await testChatCompletion(testCase);
    
    // 等待一下避免请求过快
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('🎉 所有测试完成');
}

async function main() {
  const args = Deno.args;
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log('Cassidy OpenAI Proxy 测试脚本');
    console.log('');
    console.log('用法:');
    console.log('  deno run --allow-net test-proxy.ts [选项]');
    console.log('');
    console.log('选项:');
    console.log('  --health    只测试健康检查');
    console.log('  --models    只测试模型列表');
    console.log('  --chat      只测试聊天完成');
    console.log('  --help, -h  显示帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  deno run --allow-net test-proxy.ts --health');
    console.log('  deno run --allow-net test-proxy.ts --chat');
    return;
  }
  
  if (args.includes('--health')) {
    await testHealthCheck();
  } else if (args.includes('--models')) {
    await testModels();
  } else if (args.includes('--chat')) {
    for (const testCase of testCases) {
      await testChatCompletion(testCase);
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  } else {
    await runAllTests();
  }
}

if (import.meta.main) {
  main().catch(console.error);
}
