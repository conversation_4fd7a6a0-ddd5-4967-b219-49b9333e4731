#!/usr/bin/env deno run --allow-net --allow-env

/**
 * 简单测试脚本 - 验证思考内容流式输出
 */

const PROXY_URL = 'http://localhost:8000';
const AUTH_KEY = Deno.env.get('AUTH_KEY');

async function testStreamingThinking() {
  console.log('🧠 测试思考内容流式输出');
  console.log('=' .repeat(50));
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };
  
  if (AUTH_KEY) {
    headers['Authorization'] = `Bearer ${AUTH_KEY}`;
  }
  
  try {
    const response = await fetch(`${PROXY_URL}/v1/chat/completions`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        model: 'cassidy-claude-3-7-sonnet-thinking',
        messages: [
          { role: 'user', content: '你是谁？请简单介绍一下你自己' }
        ],
        stream: true
      })
    });
    
    if (!response.ok) {
      console.log('❌ 请求失败:', response.status);
      const errorData = await response.json();
      console.log(errorData);
      return;
    }
    
    console.log('✅ 开始接收流式响应:');
    console.log('-'.repeat(50));
    
    const reader = response.body?.getReader();
    const decoder = new TextDecoder();
    
    if (!reader) {
      console.log('❌ 无法获取流式响应');
      return;
    }
    
    let buffer = '';
    let hasThinking = false;
    let hasActualContent = false;
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (line.startsWith('data: ') && line !== 'data: [DONE]') {
            try {
              const data = JSON.parse(line.substring(6));
              const content = data.choices?.[0]?.delta?.content;
              if (content) {
                // 检测思考内容
                if (content.includes('<think>')) {
                  hasThinking = true;
                  console.log('\n🧠 [思考开始]');
                }
                
                // 检测实际回复
                if (content.includes('</think>')) {
                  hasActualContent = true;
                  console.log('\n💬 [回复开始]');
                }
                
                process.stdout.write(content);
              }
            } catch (e) {
              // 忽略解析错误
            }
          } else if (line === 'data: [DONE]') {
            console.log('\n');
            console.log('-'.repeat(50));
            console.log('✅ 流式响应完成');
            break;
          }
        }
      }
      
      // 验证结果
      if (hasThinking && hasActualContent) {
        console.log('🎉 成功！思考内容和实际回复都已流式输出');
      } else {
        console.log('⚠️  警告：');
        if (!hasThinking) console.log('   - 未检测到思考内容');
        if (!hasActualContent) console.log('   - 未检测到实际回复');
      }
      
    } finally {
      reader.releaseLock();
    }
    
  } catch (error) {
    console.log('❌ 测试错误:', error.message);
  }
}

if (import.meta.main) {
  testStreamingThinking().catch(console.error);
}
