#!/usr/bin/env deno run --allow-net --allow-env

/**
 * 测试思考内容提取的脚本
 */

const PROXY_URL = 'http://localhost:8000';
const AUTH_KEY = Deno.env.get('AUTH_KEY');

async function testThinkingExtraction() {
  console.log('🧠 测试思考内容提取');
  console.log('=' .repeat(50));
  
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };
  
  if (AUTH_KEY) {
    headers['Authorization'] = `Bearer ${AUTH_KEY}`;
  }
  
  // 测试非流式响应
  console.log('\n📝 测试非流式响应:');
  try {
    const response = await fetch(`${PROXY_URL}/v1/chat/completions`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        model: 'cassidy-claude-3-7-sonnet-thinking',
        messages: [
          { role: 'user', content: '你是谁？请简单介绍一下你自己' }
        ],
        stream: false
      })
    });
    
    if (response.ok) {
      const data = await response.json();
      const content = data.choices?.[0]?.message?.content || '';
      
      console.log('✅ 响应成功');
      console.log('📄 完整内容:');
      console.log(content);
      
      // 检查是否包含思考标签
      if (content.includes('<think>') && content.includes('</think>')) {
        console.log('✅ 思考内容已正确提取');
        
        // 提取思考部分
        const thinkMatch = content.match(/<think>([\s\S]*?)<\/think>/);
        if (thinkMatch) {
          console.log('🧠 思考内容:');
          console.log(thinkMatch[1].trim());
        }
        
        // 提取回复部分
        const replyContent = content.replace(/<think>[\s\S]*?<\/think>\s*/, '');
        console.log('💬 回复内容:');
        console.log(replyContent.trim());
      } else {
        console.log('❌ 未找到思考标签');
      }
    } else {
      console.log('❌ 请求失败:', response.status);
      const errorData = await response.json();
      console.log(errorData);
    }
  } catch (error) {
    console.log('❌ 测试错误:', error.message);
  }
  
  console.log('\n' + '-'.repeat(50));
  
  // 测试流式响应
  console.log('\n🌊 测试流式响应:');
  try {
    const response = await fetch(`${PROXY_URL}/v1/chat/completions`, {
      method: 'POST',
      headers,
      body: JSON.stringify({
        model: 'cassidy-claude-3-7-sonnet-thinking',
        messages: [
          { role: 'user', content: '请解释什么是人工智能' }
        ],
        stream: true
      })
    });
    
    if (response.ok) {
      console.log('✅ 流式响应开始');
      
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (!reader) {
        console.log('❌ 无法获取流式响应');
        return;
      }
      
      let buffer = '';
      let fullContent = '';
      let hasThinking = false;
      
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;
          
          buffer += decoder.decode(value, { stream: true });
          const lines = buffer.split('\n');
          buffer = lines.pop() || '';
          
          for (const line of lines) {
            if (line.startsWith('data: ') && line !== 'data: [DONE]') {
              try {
                const data = JSON.parse(line.substring(6));
                const content = data.choices?.[0]?.delta?.content;
                if (content) {
                  fullContent += content;
                  
                  // 检查是否包含思考标签
                  if (content.includes('<think>')) {
                    hasThinking = true;
                    console.log('🧠 检测到思考内容开始');
                  }
                  
                  process.stdout.write(content);
                }
              } catch (e) {
                // 忽略解析错误
              }
            } else if (line === 'data: [DONE]') {
              console.log('\n✅ 流式响应完成');
              break;
            }
          }
        }
        
        if (hasThinking) {
          console.log('✅ 流式响应中包含思考内容');
        } else {
          console.log('❌ 流式响应中未检测到思考内容');
        }
        
        console.log('\n📄 完整内容长度:', fullContent.length);
        
      } finally {
        reader.releaseLock();
      }
    } else {
      console.log('❌ 流式请求失败:', response.status);
      const errorData = await response.json();
      console.log(errorData);
    }
  } catch (error) {
    console.log('❌ 流式测试错误:', error.message);
  }
  
  console.log('\n🎉 思考内容测试完成');
}

if (import.meta.main) {
  testThinkingExtraction().catch(console.error);
}
